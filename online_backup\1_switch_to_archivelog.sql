-- =====================================================================
-- Oracle数据库归档模式切换脚本 - 优化版
-- 功能：将数据库从NOARCHIVELOG模式切换到ARCHIVELOG模式
-- 适用：Oracle 11gR2及以上版本
-- 注意：此操作需要重启数据库，请在维护窗口期间执行
-- =====================================================================

-- 设置SQL*Plus环境
SET ECHO ON
SET FEEDBACK ON
SET PAGESIZE 1000
SET LINESIZE 200
SET TIMING ON

-- 显示当前时间和用户信息
SELECT 'Script Start Time: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS START_TIME FROM DUAL;
SELECT 'Connected as: ' || USER AS CURRENT_USER FROM DUAL;

-- 检查当前数据库状态
PROMPT =====================================================================
PROMPT 检查当前数据库状态
PROMPT =====================================================================

SELECT
    'Database Name: ' || NAME AS DB_INFO,
    'Database ID: ' || DBID AS DB_ID,
    'Archive Mode: ' || LOG_MODE AS ARCHIVE_STATUS,
    'Database Role: ' || DATABASE_ROLE AS DB_ROLE
FROM V$DATABASE;

SELECT
    'Instance Name: ' || INSTANCE_NAME AS INST_INFO,
    'Instance Status: ' || STATUS AS INST_STATUS,
    'Startup Time: ' || TO_CHAR(STARTUP_TIME, 'YYYY-MM-DD HH24:MI:SS') AS STARTUP_TIME
FROM V$INSTANCE;

-- 检查当前归档日志配置
PROMPT =====================================================================
PROMPT 检查当前归档日志配置
PROMPT =====================================================================

SELECT
    DEST_ID,
    DESTINATION,
    STATUS,
    BINDING
FROM V$ARCHIVE_DEST
WHERE DEST_ID <= 5 AND STATUS != 'INACTIVE';

-- 显示当前日志文件信息
SELECT
    GROUP#,
    THREAD#,
    SEQUENCE#,
    BYTES/1024/1024 AS SIZE_MB,
    STATUS,
    ARCHIVED
FROM V$LOG
ORDER BY GROUP#;

-- 检查是否已经是归档模式
DECLARE
    v_log_mode VARCHAR2(20);
BEGIN
    SELECT LOG_MODE INTO v_log_mode FROM V$DATABASE;

    IF v_log_mode = 'ARCHIVELOG' THEN
        DBMS_OUTPUT.PUT_LINE('警告：数据库已经处于归档模式，无需重复设置！');
        DBMS_OUTPUT.PUT_LINE('当前归档模式：' || v_log_mode);
        -- 不退出，继续显示当前配置信息
    ELSE
        DBMS_OUTPUT.PUT_LINE('当前数据库处于非归档模式：' || v_log_mode);
        DBMS_OUTPUT.PUT_LINE('准备切换到归档模式...');
    END IF;
END;
/

-- 创建归档日志目录（通过操作系统命令）
PROMPT =====================================================================
PROMPT 准备归档日志目录
PROMPT =====================================================================

-- 显示将要创建的目录
SELECT 'Archive Log Directory: /opt/oracle/archivelog' AS ARCHIVE_DIR FROM DUAL;

-- 备份当前参数文件
PROMPT =====================================================================
PROMPT 备份当前参数文件
PROMPT =====================================================================

CREATE PFILE='/opt/oracle/rman_backup/init_backup_before_archivelog.ora' FROM SPFILE;

PROMPT 参数文件已备份到: /opt/oracle/rman_backup/init_backup_before_archivelog.ora

-- 开始切换归档模式
PROMPT =====================================================================
PROMPT 开始切换到归档模式
PROMPT =====================================================================

PROMPT 步骤1: 关闭数据库（立即关闭，确保无活动连接）
SHUTDOWN IMMEDIATE;

PROMPT 步骤2: 启动数据库到MOUNT状态
STARTUP MOUNT;

PROMPT 步骤3: 配置FRA参数（单FRA模式）
-- 设置FRA目录和大小
ALTER SYSTEM SET db_recovery_file_dest='/opt/oracle/flash_recovery_area' SCOPE=SPFILE;
ALTER SYSTEM SET db_recovery_file_dest_size=40G SCOPE=SPFILE;

PROMPT 步骤4: 设置归档日志参数（单FRA模式）
-- 设置归档日志目标为FRA
ALTER SYSTEM SET log_archive_dest_1='LOCATION=USE_DB_RECOVERY_FILE_DEST VALID_FOR=(ALL_LOGFILES,ALL_ROLES) DB_UNIQUE_NAME=PDBQZ' SCOPE=SPFILE;

-- 清除其他归档目标（确保单FRA模式）
ALTER SYSTEM SET log_archive_dest_2='' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_3='' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_4='' SCOPE=SPFILE;
ALTER SYSTEM SET log_archive_dest_5='' SCOPE=SPFILE;

-- 设置归档日志格式
ALTER SYSTEM SET log_archive_format='arch_%t_%s_%r.arc' SCOPE=SPFILE;

-- 启用归档日志目标
ALTER SYSTEM SET log_archive_dest_state_1='ENABLE' SCOPE=SPFILE;

-- 设置归档日志删除策略（可选）
ALTER SYSTEM SET log_archive_min_succeed_dest=1 SCOPE=SPFILE;

-- 对于Oracle 11g，设置自动归档（某些版本需要）
ALTER SYSTEM SET log_archive_start=TRUE SCOPE=SPFILE;

PROMPT 步骤5: 启用归档模式
ALTER DATABASE ARCHIVELOG;

PROMPT 步骤6: 打开数据库
ALTER DATABASE OPEN;

-- 验证FRA和归档模式切换结果
PROMPT =====================================================================
PROMPT 验证FRA和归档模式切换结果
PROMPT =====================================================================

-- 显示归档模式状态
ARCHIVE LOG LIST;

-- 检查FRA配置
SELECT
    'FRA Destination: ' || NAME AS FRA_DEST,
    'FRA Size: ' || ROUND(SPACE_LIMIT/1024/1024/1024, 2) || ' GB' AS FRA_SIZE,
    'FRA Used: ' || ROUND(SPACE_USED/1024/1024/1024, 2) || ' GB' AS FRA_USED,
    'FRA Usage: ' || ROUND((SPACE_USED/SPACE_LIMIT)*100, 2) || '%' AS FRA_USAGE_PCT
FROM V$RECOVERY_FILE_DEST;

-- 详细检查归档配置
SELECT
    'Database Name: ' || NAME AS DB_INFO,
    'Archive Mode: ' || LOG_MODE AS ARCHIVE_STATUS,
    'Switch Time: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS SWITCH_TIME
FROM V$DATABASE;

-- 检查归档目标配置
SELECT
    DEST_ID,
    DESTINATION,
    STATUS,
    BINDING,
    VALID_FOR
FROM V$ARCHIVE_DEST
WHERE DEST_ID = 1;

-- 检查当前日志状态
SELECT
    GROUP#,
    THREAD#,
    SEQUENCE#,
    BYTES/1024/1024 AS SIZE_MB,
    STATUS,
    ARCHIVED,
    FIRST_CHANGE#
FROM V$LOG
ORDER BY GROUP#;

-- 强制日志切换以测试归档功能
PROMPT =====================================================================
PROMPT 测试归档功能
PROMPT =====================================================================

PROMPT 执行日志切换测试归档功能...
ALTER SYSTEM SWITCH LOGFILE;

-- 等待归档完成
PROMPT 等待归档完成...
DECLARE
    v_count NUMBER := 0;
BEGIN
    WHILE v_count < 10 LOOP
        DBMS_LOCK.SLEEP(1);
        v_count := v_count + 1;
    END LOOP;
END;
/

-- 检查归档日志生成情况
SELECT
    SEQUENCE#,
    FIRST_CHANGE#,
    NEXT_CHANGE#,
    ARCHIVED,
    APPLIED,
    DELETED,
    STATUS
FROM V$ARCHIVED_LOG
WHERE SEQUENCE# >= (SELECT MAX(SEQUENCE#)-2 FROM V$ARCHIVED_LOG)
ORDER BY SEQUENCE# DESC;

-- 最终状态确认
PROMPT =====================================================================
PROMPT 归档模式切换完成 - 最终状态确认
PROMPT =====================================================================

SELECT
    'Completion Time: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS COMPLETION_TIME,
    'Archive Mode: ' || (SELECT LOG_MODE FROM V$DATABASE) AS FINAL_STATUS
FROM DUAL;

PROMPT =====================================================================
PROMPT 重要提示：
PROMPT 1. 数据库已成功切换到归档模式
PROMPT 2. 归档日志将保存在: /opt/oracle/archivelog
PROMPT 3. 请定期清理归档日志以避免磁盘空间不足
PROMPT 4. 现在可以执行在线备份（热备份）
PROMPT 5. 建议设置归档日志的自动清理策略
PROMPT =====================================================================

EXIT;
