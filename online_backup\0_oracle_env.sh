#!/bin/bash

# Oracle 在线备份环境配置和检查脚本 - FRA模式
# 功能：专门针对Flash Recovery Area的环境配置

# =============================================================================
# 环境变量配置
# =============================================================================

# Oracle基础环境变量
export ORACLE_SID=PDBQZ
export ORACLE_HOME=/opt/oracle/product/11gR2/db
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 备份相关环境变量
export BACKUP_BASE_DIR="/opt/oracle/rman_backup"
export FRA_DIR="/opt/oracle/flash_recovery_area"
export BACKUP_LOG_DIR="$BACKUP_BASE_DIR/logs"

# FRA配置参数
export FRA_SIZE="40G"
export FRA_USAGE_THRESHOLD=80

# 创建必要的目录
mkdir -p "$BACKUP_BASE_DIR"
mkdir -p "$FRA_DIR"
mkdir -p "$BACKUP_LOG_DIR"

# =============================================================================
# 环境检查函数
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Oracle环境
check_oracle_environment() {
    log_info "开始Oracle环境检查..."
    
    # 检查当前用户
    local current_user=$(whoami)
    log_info "当前用户: $current_user"
    
    if [ "$current_user" != "oracle" ]; then
        log_warning "建议使用oracle用户运行备份脚本"
    fi
    
    # 检查环境变量
    log_info "检查环境变量..."
    if [ -z "$ORACLE_HOME" ]; then
        log_error "ORACLE_HOME未设置"
        return 1
    else
        log_success "ORACLE_HOME: $ORACLE_HOME"
    fi
    
    if [ -z "$ORACLE_SID" ]; then
        log_error "ORACLE_SID未设置"
        return 1
    else
        log_success "ORACLE_SID: $ORACLE_SID"
    fi
    
    # 检查Oracle主目录
    if [ ! -d "$ORACLE_HOME" ]; then
        log_error "Oracle主目录不存在: $ORACLE_HOME"
        return 1
    else
        log_success "Oracle主目录存在: $ORACLE_HOME"
    fi
    
    # 检查Oracle二进制文件
    if [ ! -f "$ORACLE_HOME/bin/sqlplus" ]; then
        log_error "sqlplus不存在: $ORACLE_HOME/bin/sqlplus"
        return 1
    else
        log_success "sqlplus可用"
    fi
    
    if [ ! -f "$ORACLE_HOME/bin/rman" ]; then
        log_error "rman不存在: $ORACLE_HOME/bin/rman"
        return 1
    else
        log_success "rman可用"
    fi
    
    return 0
}

# 检查数据库状态
check_database_status() {
    log_info "检查数据库状态..."
    
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
